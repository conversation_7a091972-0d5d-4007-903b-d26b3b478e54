"use client"

import { useEffect, useState } from "react"
import { cn } from "@/lib/utils"
import { List, X, ChevronDown } from "lucide-react"

interface TableOfContentsProps {
  content: string
  isMobile?: boolean
}

interface Heading {
  id: string
  text: string
  level: number
  children?: Heading[]
  parent?: string
}

export function TableOfContents({ content, isMobile = false }: TableOfContentsProps) {
  const [headings, setHeadings] = useState<Heading[]>([])
  const [activeId, setActiveId] = useState<string>("")
  const [isOpen, setIsOpen] = useState(false)
  const [expandedHeadings, setExpandedHeadings] = useState<Set<string>>(new Set())

  useEffect(() => {
    console.log('[TOC] Initializing Table of Contents component')
    
    // Extract headings from markdown content and build hierarchy
    const extractHeadings = () => {
      console.log('[TOC] Extracting headings from content')
      const headingRegex = /^(#{1,6})\s+(.+)$/gm
      const matches = Array.from(content.matchAll(headingRegex))

      const flatHeadings = matches.map((match) => {
        const level = match[1].length
        let text = match[2].trim()

        // Remove markdown formatting (bold, italic, etc.) and clean up text
        text = text
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold formatting **text**
          .replace(/\*(.*?)\*/g, '$1') // Remove italic formatting *text*
          .replace(/`(.*?)`/g, '$1') // Remove inline code `text`
          .replace(/[\(\)\[\]\{\}]/g, '') // Remove brackets
          .replace(/[^\w\s\-\.]/g, ' ') // Keep alphanumeric, spaces, hyphens, and dots
          .replace(/\s+/g, ' ') // Normalize whitespace
          .trim()

        // Generate ID that matches what the markdown renderer would create
        const id = text.toLowerCase()
          .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
          .replace(/\s+/g, '-') // Replace spaces with hyphens
          .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
          .replace(/-+/g, '-') // Collapse multiple hyphens

        console.log(`[TOC] Extracted heading: "${text}" -> ID: "${id}"`)

        return { id, text, level, children: [], parent: undefined as string | undefined }
      })

      // Build hierarchical structure
      const hierarchicalHeadings: Heading[] = []
      const stack: Heading[] = []

      flatHeadings.forEach((heading) => {
        // Remove items from stack that are at same or deeper level
        while (stack.length > 0 && stack[stack.length - 1].level >= heading.level) {
          stack.pop()
        }

        // If stack is empty, this is a top-level heading
        if (stack.length === 0) {
          hierarchicalHeadings.push(heading)
        } else {
          // Add as child to the last item in stack
          const parent = stack[stack.length - 1]
          if (!parent.children) parent.children = []
          parent.children.push(heading)
          heading.parent = parent.id
        }

        stack.push(heading)
      })

      return hierarchicalHeadings
    }

    const hierarchicalHeadings = extractHeadings()
    setHeadings(hierarchicalHeadings)

    // Auto-expand top-level headings initially
    const topLevelIds = hierarchicalHeadings.map(h => h.id)
    setExpandedHeadings(new Set(topLevelIds))
  }, [content])

  useEffect(() => {
    if (headings.length === 0) return

    // Verify headings have IDs (they should be auto-generated now)
    const verifyHeadingIds = () => {
      const allHeadings = Array.from(document.querySelectorAll("h1, h2, h3, h4, h5, h6"))


      // Check if any headings are missing IDs and add them if needed
      allHeadings.forEach((heading) => {
        if (!heading.id && heading.textContent) {
          const text = heading.textContent.trim()
          const id = text.toLowerCase()
            .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .replace(/-+/g, '-') // Collapse multiple hyphens
          heading.id = id

        }
      })
    }

    // Verify IDs immediately and after a short delay
    verifyHeadingIds()
    const timeoutId = setTimeout(verifyHeadingIds, 100)

    // Helper function to find heading in hierarchy
    const findHeadingInHierarchy = (headings: Heading[], targetId: string): Heading | null => {
      for (const heading of headings) {
        if (heading.id === targetId) return heading
        if (heading.children) {
          const found = findHeadingInHierarchy(heading.children, targetId)
          if (found) return found
        }
      }
      return null
    }

    // Helper function to expand current heading and its parent chain
    const expandCurrentHeading = (targetId: string) => {
      const heading = findHeadingInHierarchy(headings, targetId)
      if (!heading) return

      setExpandedHeadings(prev => {
        const newExpanded = new Set(prev)

        // Always expand the current active heading to show its children
        newExpanded.add(heading.id)

        // Expand all parent headings to ensure the current heading is visible
        let currentHeading = heading
        while (currentHeading.parent) {
          newExpanded.add(currentHeading.parent)
          currentHeading = findHeadingInHierarchy(headings, currentHeading.parent) || currentHeading
          if (!currentHeading.parent) break
        }

        // Optional: Collapse siblings at the same level to keep TOC clean
        // This creates a more focused view showing only the current branch
        const collapseOtherBranches = (headings: Heading[], currentPath: Set<string>) => {
          headings.forEach(h => {
            if (!currentPath.has(h.id) && h.children && h.children.length > 0) {
              // Only collapse if this heading is not in the current path
              if (!isInCurrentPath(h.id, targetId)) {
                newExpanded.delete(h.id)
              }
            }
            if (h.children) {
              collapseOtherBranches(h.children, currentPath)
            }
          })
        }

        // Build current path for smart collapsing
        const currentPath = new Set<string>()
        let pathHeading = heading
        currentPath.add(pathHeading.id)
        while (pathHeading.parent) {
          currentPath.add(pathHeading.parent)
          pathHeading = findHeadingInHierarchy(headings, pathHeading.parent) || pathHeading
          if (!pathHeading.parent) break
        }

        // Collapse other branches to keep focus on current section
        collapseOtherBranches(headings, currentPath)

        return newExpanded
      })
    }

    // Helper function to check if a heading is in the current reading path
    const isInCurrentPath = (headingId: string, targetId: string): boolean => {
      const target = findHeadingInHierarchy(headings, targetId)
      if (!target) return false

      // Check if headingId is the target or an ancestor of target
      let current: Heading | null = target
      while (current) {
        if (current.id === headingId) return true
        if (!current.parent) break
        current = findHeadingInHierarchy(headings, current.parent)
      }
      return false
    }

    // Create intersection observer with better settings
    const observer = new IntersectionObserver(
      (entries) => {
        const visibleEntries = entries.filter(entry => entry.isIntersecting)

        if (visibleEntries.length > 0) {
          // Find the entry closest to the top of the viewport
          const topEntry = visibleEntries.reduce((closest, entry) => {
            const entryTop = entry.boundingClientRect.top
            const closestTop = closest.boundingClientRect.top
            return entryTop < closestTop ? entry : closest
          })

          setActiveId(topEntry.target.id)
          // Auto-expand current heading to show its children
          expandCurrentHeading(topEntry.target.id)
        }
      },
      {
        rootMargin: "-10% 0px -80% 0px",
        threshold: [0, 0.1, 0.5, 1]
      }
    )

    // Helper function to collect all heading IDs from hierarchy
    const collectAllHeadingIds = (headings: Heading[]): string[] => {
      const ids: string[] = []
      headings.forEach(heading => {
        ids.push(heading.id)
        if (heading.children) {
          ids.push(...collectAllHeadingIds(heading.children))
        }
      })
      return ids
    }

    // Observe all headings with a delay to ensure they exist
    const observeHeadings = () => {
      const allIds = collectAllHeadingIds(headings)
      allIds.forEach((id) => {
        const element = document.getElementById(id)
        if (element) {
          observer.observe(element)
        }
      })
    }

    observeHeadings()
    const observeTimeoutId = setTimeout(observeHeadings, 200)

    // Also add a scroll listener as backup
    const handleScroll = () => {
      const allIds = collectAllHeadingIds(headings)
      const headingElements = allIds.map(id => document.getElementById(id)).filter(Boolean)

      if (headingElements.length === 0) return

      // Find the heading that's currently in view
      let currentHeading = headingElements[0]
      let closestDistance = Infinity

      for (const heading of headingElements) {
        const rect = heading!.getBoundingClientRect()
        const distance = Math.abs(rect.top - 100) // Distance from 100px from top

        if (rect.top <= 150 && distance < closestDistance) { // Within 150px from top
          currentHeading = heading
          closestDistance = distance
        }
      }

      if (currentHeading && currentHeading.id !== activeId) {

        setActiveId(currentHeading.id)
        expandCurrentHeading(currentHeading.id)
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      clearTimeout(timeoutId)
      clearTimeout(observeTimeoutId)
      observer.disconnect()
      window.removeEventListener('scroll', handleScroll)
    }
  }, [headings, activeId])

  if (headings.length === 0) {
    return null
  }

  const handleHeadingClick = (headingId: string) => {
    console.log('[TOC] Heading clicked:', headingId)
    const element = document.getElementById(headingId)
    console.log('[TOC] Found element:', element ? 'Yes' : 'No')
    
    if (element) {
      console.log('[TOC] Scrolling to element position')
      const headerOffset = 100
      const elementPosition = element.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset

      // Immediately set as active for better UX
      setActiveId(headingId)

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })

      // Close TOC on mobile after clicking
      if (isMobile) {
        setIsOpen(false)
      }

      // Helper function to find heading in hierarchy
      const findHeadingInHierarchy = (headings: Heading[], targetId: string): Heading | null => {
        for (const heading of headings) {
          if (heading.id === targetId) return heading
          if (heading.children) {
            const found = findHeadingInHierarchy(heading.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      // Expand the clicked heading and its parent chain
      setExpandedHeadings(prev => {
        const newExpanded = new Set(prev)

        // Find the clicked heading
        const clickedHeading = findHeadingInHierarchy(headings, headingId)
        if (clickedHeading) {
          // Expand the clicked heading to show its children
          newExpanded.add(headingId)

          // Expand all parent headings to ensure the clicked heading is visible
          let currentHeading = clickedHeading
          while (currentHeading.parent) {
            newExpanded.add(currentHeading.parent)
            currentHeading = findHeadingInHierarchy(headings, currentHeading.parent) || currentHeading
            if (!currentHeading.parent) break
          }
        }

        return newExpanded
      })
    }
  }

  const toggleHeading = (headingId: string) => {
    setExpandedHeadings(prev => {
      const newExpanded = new Set(prev)
      if (newExpanded.has(headingId)) {
        newExpanded.delete(headingId)
      } else {
        newExpanded.add(headingId)
      }
      return newExpanded
    })
  }

  // Recursive component to render hierarchical headings
  const renderHeading = (heading: Heading, depth: number = 0) => {
    const hasChildren = heading.children && heading.children.length > 0
    const isExpanded = expandedHeadings.has(heading.id)
    const isActive = activeId === heading.id
    const indentLevel = Math.min(depth, 3) // Limit indentation to 3 levels

    return (
      <div 
        key={heading.id} 
        className={cn(
          "group relative",
          {
            "ml-1 border-l-2 border-gray-200": depth > 0,
            "pl-3": depth > 0
          }
        )}
      >
        <div className="flex items-start">
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                toggleHeading(heading.id)
              }}
              aria-label={isExpanded ? 'Collapse section' : 'Expand section'}
              aria-expanded={isExpanded}
              className={cn(
                "p-1.5 rounded-md transition-all flex-shrink-0 mt-0.5",
                "text-gray-500 hover:bg-gray-100 hover:text-gray-700",
                "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-1",
                
              )}
            >
              <ChevronDown
                className={cn(
                  "h-3.5 w-3.5 transition-transform duration-200",
                  isExpanded ? "rotate-0" : "-rotate-90"
                )}
                aria-hidden="true"
              />
            </button>
          )}
          <button
            onClick={() => handleHeadingClick(heading.id)}
            style={{
              paddingLeft: `${Math.max(0.05, indentLevel * 0.2)}rem`,
              marginLeft: depth > 0 ? '.1rem' : '0'
            }}
            className={cn(
              "flex-1 text-left py-2 px-2.5 rounded-md text-sm transition-all duration-200 leading-snug",
              "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-1",
              isActive
                ? "text-primary-700 font-semibold bg-primary-50"
                : "text-gray-700 hover:bg-gray-100",
              {
                "ml-2": hasChildren
              }
            )}
            aria-current={isActive ? 'location' : undefined}
          >
            <span 
              className={cn(
                "line-clamp-2 relative",
                "before:absolute before:-left-1 before:top-1/2 before:hidden before:h-1 before:w-1 before:-translate-y-1/2 before:rounded-full",
                isActive && "before:block before:bg-primary-500"
              )}
            >
              {heading.text}
            </span>
          </button>
        </div>
        {hasChildren && isExpanded && (
          <div className="ml-4 mt-1 space-y-0.5">
            {heading.children!.map(child => renderHeading(child, depth + 1))}
          </div>
        )}
      </div>
    )
  }

  // Mobile FAB version
  if (isMobile) {
    return (
      <>
        {/* FAB Button - Top right position on mobile */}
        <button
          onClick={() => setIsOpen(true)}
          className="fixed top-6 right-6 z-50 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-200 hover:scale-105"
          aria-label="Table of Contents"
        >
          <List className="h-5 w-5" />
        </button>

        {/* Mobile Modal */}
        {isOpen && (
          <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm" onClick={() => setIsOpen(false)}>
            <div
              className="fixed bottom-0 left-0 right-0 bg-background border border-border rounded-t-xl max-h-[70vh] overflow-hidden shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between p-4 border-b border-border">
                <h3 className="font-semibold text-lg text-foreground">On this page</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 hover:bg-secondary rounded-full transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="p-4 overflow-y-auto max-h-[calc(70vh-5rem)]">
                <nav className="space-y-1">
                  {headings.map(heading => renderHeading(heading))}
                </nav>
              </div>
            </div>
          </div>
        )}
      </>
    )
  }

  // Desktop version
  return (
    <nav className="space-y-0.5">
      {headings.map(heading => renderHeading(heading))}
    </nav>
  )
}
