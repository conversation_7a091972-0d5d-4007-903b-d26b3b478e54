"use client"

import Link from "next/link"
import { MainNav } from "@/components/main-nav"
import { Container } from "@/components/ui/container"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, ArrowUpRight, Download, UserRound, Flag, ChevronDown, BookCheck, StoreIcon } from 'lucide-react'
import Image from "next/image"
import { useState } from 'react'
import { cn } from '@/lib/utils'

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [expandedCategories, setExpandedCategories] = useState<string[]>([])

  const closeMobileMenu = () => {
    setMobileMenuOpen(false)
    setExpandedCategories([])
  }

  const toggleCategory = (categoryTitle: string) => {
    setExpandedCategories(prev => 
      prev.includes(categoryTitle) 
        ? prev.filter(title => title !== categoryTitle)
        : [...prev, categoryTitle]
    )
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b border-gray-200 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <Container>
        <div className="flex h-16 items-center justify-between w-full">
          <div className="flex items-center gap-2">
            <Link href="/" className="flex items-center">
              <Image 
                src="/logos/Swadesic logo with name.svg" 
                alt="Swadesic Logo" 
                width={48}
                height={48}
                className="h-12 w-auto mr-2"
              />
              {/* <span className="text-xl">Swadesic</span> */}
            </Link>
            {/* Desktop Navigation */}
            <div className="hidden md:flex md:gap-10 md:ml-6">
              <MainNav />
            </div>
          </div>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-end gap-3">
            <Button className="bg-black text-white hover:bg-black/90" asChild>
              <Link 
                href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&hl=en_SG" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                Download the App <ArrowUpRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button className="bg-white text-black border-black hover:bg-black hover:text-white transition-colors" asChild>
            <Link 
                href="https://swadesic.com/"
                target="_blank"
                rel="noopener noreferrer"
              >
              Start Now <ArrowUpRight className="ml-2 h-4 w-4" />
            </Link>
            </Button>
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden flex items-center gap-2">
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="bottom" className="p-6 h-[80vh] overflow-y-auto w-full">
                <nav className="flex flex-col gap-2 max-w-full">
                  {/* Consumer Section */}
                  <div className="pt-2 space-y-4">
                    <div 
                      className="flex items-center justify-between cursor-pointer" 
                      onClick={() => toggleCategory("For Consumers")}
                    >
                      <div className="flex items-center gap-2 text-lg font-semibold">
                        <UserRound className="h-5 w-5 text-[var(--brand-color)]" />
                        For Consumers
                      </div>
                      <ChevronDown 
                        className={cn(
                          "h-5 w-5 transition-transform duration-200",
                          expandedCategories.includes("For Consumers") ? "transform rotate-180" : ""
                        )} 
                      />
                    </div>
                    <div 
                      className={cn(
                        "transition-all duration-200 overflow-hidden",
                        expandedCategories.includes("For Consumers") ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                      )}
                    >
                      <div className="pl-7 space-y-4">
                        <Link href="/" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Being Swadeshi Consumer</span>
                          <span className="text-sm text-muted-foreground">Discover Small businesses across Bharat</span>
                        </Link>
                        <Link href="/consumer-pricing" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Pricing</span>
                          <span className="text-sm text-muted-foreground">Unlock Revenue sharing with Swadesic Premium</span>
                        </Link>
                      </div>
                    </div>
                  </div>
                  
                  {/* Business Section */}
                  <div className="pt-2 space-y-4">
                    <div 
                      className="flex items-center justify-between cursor-pointer" 
                      onClick={() => toggleCategory("For Business")}
                    >
                      <div className="flex items-center gap-2 text-lg font-semibold">
                        <StoreIcon className="h-5 w-5 text-[var(--brand-color)]" />
                        For Business
                      </div>
                      <ChevronDown 
                        className={cn(
                          "h-5 w-5 transition-transform duration-200",
                          expandedCategories.includes("For Business") ? "transform rotate-180" : ""
                        )} 
                      />
                    </div>
                    <div 
                      className={cn(
                        "transition-all duration-200 overflow-hidden",
                        expandedCategories.includes("For Business") ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                      )}
                    >
                      <div className="pl-7 space-y-4">
                        <Link href="/business" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Register Your Business</span>
                          <span className="text-sm text-muted-foreground">Grow & Scale your business online</span>
                        </Link>
                        <Link href="/business/benefits" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Benefits with Swadesic</span>
                          <span className="text-sm text-muted-foreground">Discover our powerful features</span>
                        </Link>
                        <Link href="/business/pricing" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Pricing</span>
                          <span className="text-sm text-muted-foreground">Simple, transparent pricing</span>
                        </Link>
                        <Link href="/business/faqs" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Business FAQs</span>
                          <span className="text-sm text-muted-foreground">Get answers to common questions</span>
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Swadeshi Mission Section */}
                  <div className="pt-2 space-y-4">
                    <div 
                      className="flex items-center justify-between cursor-pointer" 
                      onClick={() => toggleCategory("Swadeshi Mission")}
                    >
                      <div className="flex items-center gap-2 text-lg font-semibold">
                        <Flag className="h-5 w-5 text-[var(--brand-color)]" />
                        Swadeshi Mission
                      </div>
                      <ChevronDown 
                        className={cn(
                          "h-5 w-5 transition-transform duration-200",
                          expandedCategories.includes("Swadeshi Mission") ? "transform rotate-180" : ""
                        )} 
                      />
                    </div>
                    <div 
                      className={cn(
                        "transition-all duration-200 overflow-hidden",
                        expandedCategories.includes("Swadeshi Mission") ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                      )}
                    >
                      <div className="pl-7 space-y-4">
                        <Link href="/swadeshi-movement-2" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Swadeshi Movement 2.0</span>
                          <span className="text-sm text-muted-foreground">Learn about the movement</span>
                        </Link>
                        <Link href="/backstory" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Backstory</span>
                          <span className="text-sm text-muted-foreground">Our journey and mission</span>
                        </Link>
                        <Link href="https://sociallyx.com" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Our Parent Company</span>
                          <span className="text-sm text-muted-foreground">Visit Socially X</span>
                        </Link>
                        <Link href="https://sociallyx.com/team" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Team</span>
                          <span className="text-sm text-muted-foreground">Meet the team behind our Products</span>
                        </Link>
                        <Link href="/contact" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Contact Us</span>
                          <span className="text-sm text-muted-foreground">Get in touch with us</span>
                        </Link>
                      </div>
                    </div>
                  </div>
                  {/* Blog Section */}
                  <div className="pt-2 space-y-4">
                    <div className="flex items-center justify-between cursor-pointer" onClick={() => toggleCategory("Blog")}>
                      <div className="flex items-center gap-2 text-lg font-semibold">
                        <BookCheck className="h-5 w-5 text-[var(--brand-color)]" />
                        Blog
                      </div>
                      <ChevronDown 
                        className={cn(
                          "h-5 w-5 transition-transform duration-200",
                          expandedCategories.includes("Blog") ? "transform rotate-180" : ""
                        )}
                      />
                    </div>
                    <div 
                      className={cn(
                        "transition-all duration-200 overflow-hidden",
                        expandedCategories.includes("Blog") ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"
                      )}
                    >
                      <div className="pl-7 space-y-4">
                        <Link href="/blog" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Recent Posts</span>
                          <span className="text-sm text-muted-foreground">Read our latest articles</span>
                        </Link>
                        <Link href="/blog/category" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Categories</span>
                          <span className="text-sm text-muted-foreground">Explore by topics</span>
                        </Link>
                        <Link href="/blog/authors" className="flex flex-col text-base hover:text-foreground/80" onClick={closeMobileMenu}>
                          <span className="font-medium">Authors</span>
                          <span className="text-sm text-muted-foreground">Meet the writers</span>
                        </Link>
                      </div>
                    </div>
                  </div>

                  {/* Divider */}
                  <div className="border-t border-gray-200 my-2"></div>
                  
                 
                  {/* Download App Button */}
                  <div className="pt-4">
                    <Button className="w-full bg-black text-white hover:bg-black/90" asChild>
                      <Link 
                        href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&hl=en_SG" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        onClick={closeMobileMenu}
                      >
                        <Download className="mr-2 h-4 w-4" /> Download the App
                      </Link>
                    </Button>
                  </div>
                </nav>
              </SheetContent>
            </Sheet>
            <Button variant="ghost" size="icon" className="bg-black text-white hover:bg-black/90" asChild>
              <Link 
                href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic&hl=en_SG" 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <Download className="h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </Container>
    </header>
  )
}
