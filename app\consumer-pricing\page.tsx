import { ArrowR<PERSON>, Asterisk } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

const plans = [
  {
    name: "Free Plan",
    forWhomPrimary: "All access to Swadesic's business & social features, product browsing, and community interactions.",
    forWhomSecondary: "For Casual Buyers",
    price: "₹0",
    interval: "month",
    cta: "Start Shopping",
    benefits: [
      "All access to Swadesic's business & social features",
      "Product browsing and community interactions",
    ]
  },
  {
    name: "Premium Plan",
    forWhomPrimary: "Perfect for content creators & influencers who want to earn credibility, trust and recognition for their efforts.",
    forWhomSecondary: "Best for Content Creators",
    price: "₹299",
    interval: "month",
    popular: true,
    cta: "Go Premium",
    benefits: [
      "Everything in Free plan",
      "Premium Badge displayed next to your handle",
      "Optional ID verification for credibility",
      "Priority Support: Access to priority customer support",
      "Early access to Swadesic's latest features"
    ]
  }
]

export default function PricingPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <section className="container py-8 md:py-12 lg:py-24">
          <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
            <h1 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
              Choose Your Shopping Experience
            </h1>
            <p className="max-w-[85%] leading-normal text-gray-600 sm:text-lg sm:leading-7">
              Select a plan that matches your shopping style and earning goals
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2 mx-auto max-w-5xl pt-8">
            {plans.map((plan) => (
              <div
                key={plan.name}
                className={`relative flex flex-col rounded-2xl border-2 bg-white p-6 shadow-lg ${
                  plan.popular ? "border-[var(--brand-color)]" : "border-gray-900"
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-5 left-0 right-0 mx-auto w-fit rounded-full bg-[var(--brand-color)] px-3 py-1 text-sm font-semibold text-white">
                    Recommended
                  </div>
                )}
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                  <p className="text-gray-600 text-sm">{plan.forWhomPrimary}</p>
                  <p className="text-gray-900 font-semibold">{plan.forWhomSecondary}</p>
                  <div className="flex items-baseline">
                    <span className="text-3xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600 ml-1">/{plan.interval}</span>
                  </div>
                </div>
                <div className="mt-8">
                  <Button 
                    className={`w-full ${
                      plan.popular 
                        ? "bg-[var(--brand-color)] border-2 border-[var(--brand-color)] text-white hover:bg-[var(--brand-color)]/90" 
                        : "bg-white text-black border-2 border-black hover:bg-black/5"
                    }`}
                    asChild
                  >
                    <a 
                      href="https://play.google.com/store/apps/details?id=com.sociallyx.swadesic" 
                      target="_blank" 
                      rel="noopener noreferrer"
                    >
                      {plan.cta}
                    </a>
                  </Button>
                </div>
                <ul className="mt-8 space-y-4">
                  {plan.benefits.map((benefit) => (
                    <li key={benefit} className="grid grid-cols-[20px_1fr] gap-3">
                      <Asterisk className="h-5 w-5 text-black" />
                      <span className="text-black">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="mx-auto max-w-[58rem] text-center mt-24">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Turn Your Shopping Into Passive Income</h2>
            <p className="text-black mb-6">
              Help us reaching more Swadeshi stores and help them growby referring them to Swadesic and Earn a passive income for life.
            </p>
            <Button 
              className="bg-white text-black border-2 border-black hover:bg-black/5"
              asChild
            >
              <a href="/affiliate-program">Learn More About Revenue Sharing
              <ArrowRight className="ml-2 h-4 w-4" />
              </a>
            </Button>
          </div>
        </section>
      </main>
    </div>
  )
}
