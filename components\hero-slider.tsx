"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>Left, ArrowRight, ArrowUpRight, Pause, Play } from "lucide-react"
import { cn } from "@/lib/utils"

export interface SlideData {
  headline: string
  subheadline: string
  cta: string
  label: string
}

interface HeroSliderProps {
  slides: SlideData[]
  interval?: number // in milliseconds
  className?: string
}

export function HeroSlider({ slides, interval = 5000, className }: HeroSliderProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPaused, setIsPaused] = useState(false)
  const [progress, setProgress] = useState(0)
  const [isVisible, setIsVisible] = useState(true)
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const slideStartTime = useRef<number>(Date.now())
  const heroRef = useRef<HTMLDivElement>(null)

  // Function to go to a specific slide
  const goToSlide = (index: number) => {
    setCurrentSlide((index + slides.length) % slides.length)
    setProgress(0)
    slideStartTime.current = Date.now()
  }

  // Function to navigate to next/previous slide
  const navigateSlide = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'next' 
      ? (currentSlide + 1) % slides.length 
      : (currentSlide - 1 + slides.length) % slides.length
    goToSlide(newIndex)
  }

  // Function to toggle pause/play
  const togglePause = () => {
    const now = Date.now()
    if (isPaused) {
      // If resuming, update the start time to now minus the elapsed time
      slideStartTime.current = now - ((interval * progress) / 100)
    }
    setIsPaused(!isPaused)
  }

  // Update progress bar
  useEffect(() => {
    if (isPaused) {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current)
      }
      return
    }

    progressIntervalRef.current = setInterval(() => {
      const elapsed = Date.now() - slideStartTime.current
      const newProgress = (elapsed / interval) * 100
      
      if (newProgress >= 100) {
        setCurrentSlide((prev) => (prev + 1) % slides.length)
        setProgress(0)
        slideStartTime.current = Date.now()
      } else {
        setProgress(newProgress)
      }
    }, 50) // Update progress every 50ms for smooth animation

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current)
      }
    }
  }, [isPaused, interval, slides.length, progress])

  // Reset progress when slide changes
  useEffect(() => {
    setProgress(0)
    slideStartTime.current = Date.now()
  }, [currentSlide])

  // Handle scroll visibility
  useEffect(() => {
    const handleScroll = () => {
      if (!heroRef.current) return
      
      const heroBottom = heroRef.current.getBoundingClientRect().bottom
      // Hide when scrolled past the hero section
      setIsVisible(window.scrollY < heroBottom - 200)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Get the current slide data
  const currentSlideData = slides[currentSlide]

  return (
    <div>
      {/* Hero Content */}
      <div className={cn("relative w-full min-h-[500px] h-auto md:h-[600px] flex flex-col", className)}>
        <div className="relative flex-1 overflow-hidden">
          <div className="mx-auto max-w-[1200px] space-y-4 text-center px-4 py-8 h-full flex flex-col items-center justify-center">
            <p className="text-sm text-muted-foreground">
              Swadesic
            </p>
            <h1 className="font-heading text-4xl leading-[1.1] md:text-6xl">
              {currentSlideData.headline}
            </h1>
            <p className="text-base text-muted-foreground">
              {currentSlideData.subheadline}
            </p>
            <div className="flex flex-col gap-6 mb-16">
              <Button
                size="lg"
                className="mx-auto text-white bg-black hover:bg-black/90 rounded-full"
              >
                {currentSlideData.cta}
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Slider Controls Overlay */}
      <div 
        ref={heroRef}
        className={cn(
          "fixed bottom-8 left-1/2 -translate-x-1/2 z-50 w-full max-w-4xl px-4 transition-all duration-300",
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4 pointer-events-none'
        )}
      >
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl p-4">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-between gap-4">
              {/* Left Arrow */}
              <button 
                onClick={() => navigateSlide('prev')}
                className="p-2 text-gray-500 hover:text-black transition-colors"
                aria-label="Previous slide"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>

              {/* Active Label and Progress */}
              <div className="flex-1">
                {/* Active Label */}
                <div className="flex justify-center items-center mb-2">
                  <div className="text-sm font-medium text-black px-2 py-1">
                    {currentSlideData.label}
                  </div>
                </div>
                
                {/* Progress Bar with Individual Segments */}
                <div className="relative h-1.5 max-w-2xl mx-auto">
                  <div className="absolute inset-0 flex gap-1">
                    {slides.map((_, idx) => {
                      const isActive = idx === currentSlide;
                      const isCompleted = idx < currentSlide;
                      const segmentProgress = isActive ? (progress / 100) * 100 : isCompleted ? 100 : 0;
                      
                      return (
                        <div 
                          key={idx}
                          className="h-full bg-gray-200 rounded-full flex-1 overflow-hidden relative"
                          onClick={() => goToSlide(idx)}
                        >
                          <div 
                            className="absolute left-0 top-0 h-full bg-black transition-all duration-300 ease-linear"
                            style={{
                              width: `${segmentProgress}%`,
                              opacity: isActive || isCompleted ? 1 : 0,
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* Right Arrow */}
              <button 
                onClick={() => navigateSlide('next')}
                className="p-2 text-gray-500 hover:text-black transition-colors"
                aria-label="Next slide"
              >
                <ArrowRight className="h-5 w-5" />
              </button>
            </div>

            {/* Progress Indicator and Controls */}
            <div className="flex justify-between items-center mt-4">
              <button
                onClick={togglePause}
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-black transition-colors"
              >
                {isPaused ? (
                  <>
                    <Play className="h-4 w-4" />
                    <span>Play</span>
                  </>
                ) : (
                  <>
                    <Pause className="h-4 w-4" />
                    <span>Pause</span>
                  </>
                )}
              </button>
              
              <div className="text-sm text-gray-600">
                {currentSlide + 1} / {slides.length}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
