"use client";

import React, { ReactNode, ComponentProps } from "react";
import ReactMarkdown from "react-markdown";
import { Prism as Syntax<PERSON>ighlighter, SyntaxHighlighterProps } from "react-syntax-highlighter";
import { oneDark, oneLight } from "react-syntax-highlighter/dist/cjs/styles/prism";
import Image from "next/image";
import { ExternalLink } from "lucide-react";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import { useTheme } from "next-themes";

// Type definitions for list components
interface BaseListProps {
  depth?: number;
  ordered?: boolean;
  children?: ReactNode;
  'data-index'?: number;
}

interface UlProps extends Omit<ComponentProps<'ul'>, 'children' | 'type'>, BaseListProps {}
interface OlProps extends Omit<ComponentProps<'ol'>, 'children' | 'type'>, BaseListProps {
  type?: '1' | 'a' | 'A' | 'i' | 'I';
  reversed?: boolean;
  start?: number;
}

interface ListItemProps extends Omit<ComponentProps<'li'>, 'children'>, BaseListProps {}

interface MarkdownContentProps {
  content: string;
}

// Helper function to extract text content from React children
function extractTextFromChildren(children: ReactNode): string {
  if (typeof children === 'string') {
    return children;
  }

  if (typeof children === 'number') {
    return String(children);
  }

  if (Array.isArray(children)) {
    return children.map(extractTextFromChildren).join('');
  }

  if (React.isValidElement(children)) {
    return extractTextFromChildren(children.props.children);
  }

  return '';
}

// Helper function to generate consistent heading IDs
function generateHeadingId(children: ReactNode): string {
  const text = extractTextFromChildren(children);
  return text.toLowerCase()
    .replace(/[^\w\s\-]/g, '') // Remove special chars except hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .replace(/-+/g, '-'); // Collapse multiple hyphens
}

export function MarkdownContent({ content }: MarkdownContentProps) {
  const { theme } = useTheme()

  // Process content to remove duplicates and unwanted text
  let processedContent = content
    .replace(/^##?\s*.*?\n/, '') // Remove first heading
    .replace(/!\[.*?\]\(.*?1436129.*?\)\n?/, '') // Remove cover image
    .trim(); // Remove extra whitespace

  // Remove specific unwanted text patterns (case insensitive and flexible)
  const unwantedPatterns = [
    /^We know,?\s*we know\.?\s*We'?re spoiling you\.?\s*$/gim,
    /^But hey,?\s*you gave feedback,?\s*we listened,?\s*and now we have four updates.*$/gim,
    /^This week:?\s*enhanced marketplace features.*$/gim,
    /^Keep reading for the details\.?\s*$/gim,
    /^But hey.*$/gim,
    /^This week:.*$/gim,
  ];

  unwantedPatterns.forEach(pattern => {
    processedContent = processedContent.replace(pattern, '');
  });

  // Clean up extra whitespace and empty lines
  processedContent = processedContent
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Replace multiple empty lines with double newline
    .trim();

  return (
    <ReactMarkdown
      rehypePlugins={[rehypeRaw, rehypeSanitize]}
      remarkPlugins={[remarkGfm]}
      components={{
        h1: ({ children }) => {
          const id = generateHeadingId(children)
          return (
            <h1 id={id} className="text-4xl font-bold mb-6 mt-12 text-foreground scroll-mt-24 leading-tight border-b border-border pb-4">
              {children}
            </h1>
          )
        },
        h2: ({ children }) => {
          const id = generateHeadingId(children)
          return (
            <h2 id={id} className="text-3xl font-bold mb-5 mt-10 text-foreground scroll-mt-24 leading-tight">
              {children}
            </h2>
          )
        },
        h3: ({ children }) => {
          const id = generateHeadingId(children)
          return (
            <h3 id={id} className="text-2xl font-semibold mb-4 mt-8 text-foreground scroll-mt-24 leading-tight">
              {children}
            </h3>
          )
        },
        h4: ({ children }) => {
          const id = generateHeadingId(children)
          return (
            <h4 id={id} className="text-xl font-semibold mb-3 mt-6 text-foreground scroll-mt-24 leading-tight">
              {children}
            </h4>
          )
        },
        h5: ({ children }) => {
          const id = generateHeadingId(children)
          return (
            <h5 id={id} className="text-lg font-semibold mb-3 mt-5 text-foreground scroll-mt-24 leading-tight">
              {children}
            </h5>
          )
        },
        h6: ({ children }) => {
          const id = generateHeadingId(children)
          return (
            <h6 id={id} className="text-base font-semibold mb-2 mt-4 text-foreground scroll-mt-24 leading-tight">
              {children}
            </h6>
          )
        },
        p: ({ children }) => (
          <p className="mb-6 leading-relaxed text-base text-foreground/90">{children}</p>
        ),
        ul: ({ children, depth = 0, ...props }: UlProps) => (
          <ul 
            className="list-disc list-outside mb-6 space-y-3"
            style={{
              listStyleType: 'disc',
              paddingLeft: `${depth * 1.5 + 2}rem`,
              marginLeft: '0.5rem',
            }}
            {...props}
          >
            {React.Children.map(children, (child, index) => {
              if (React.isValidElement(child)) {
                return React.cloneElement(child, { 
                  depth: depth + 1,
                  'data-index': index 
                } as any);
              }
              return child;
            })}
          </ul>
        ),
        ol: ({ children, depth = 0, ...props }: OlProps) => {
          const listType = ['decimal', 'lower-alpha', 'lower-roman', 'upper-alpha', 'upper-roman'][depth % 5];
          const style = { '--list-counter': 'decimal' } as React.CSSProperties;
          
          return (
            <ol 
              className="list-decimal list-outside ml-6 mb-6 space-y-3"
              style={{
                listStyleType: listType as any,
                ...style
              }}
              {...props}
            >
              {React.Children.map(children, (child) => {
                if (React.isValidElement(child)) {
                  return React.cloneElement(child, { depth: depth + 1 } as any);
                }
                return child;
              })}
            </ol>
          );
        },
        li: ({ children, ordered, depth = 0, ...props }: ListItemProps) => {
          const childArray = React.Children.toArray(children);
          const hasNestedList = childArray.some(
            child => React.isValidElement(child) && 
                   (child.type === 'ol' || child.type === 'ul')
          );
          
          return (
            <li 
              className={`text-base leading-relaxed text-foreground/90 ${hasNestedList ? 'mb-3' : ''}`}
              style={{
                marginLeft: `${depth * 0.75}rem`,
                paddingLeft: '0.75rem',
                listStyleType: ordered ? 'none' : undefined,
                counterIncrement: ordered ? 'list-item' : undefined,
                position: 'relative',
              }}
              {...props}
            >
              {ordered && (
                <span className="absolute left-0 w-6 text-right">
                  {depth === 0 
                    ? `${(props['data-index'] ?? 0) + 1}.` 
                    : String.fromCharCode(97 + (props['data-index'] ?? 0)) + ')'
                  }
                </span>
              )}
              <div className="pl-2">
                {React.Children.map(children, child => {
                  if (React.isValidElement(child) && (child.type === 'ol' || child.type === 'ul')) {
                    return React.cloneElement(child, { depth: depth + 1 } as any);
                  }
                  return child;
                })}
              </div>
            </li>
          );
        },
        a: ({ href, children }) => {
          if (!href) return <>{children}</>;

          const isExternal = href.startsWith("http");
          return (
            <a
              href={href}
              target={isExternal ? "_blank" : undefined}
              rel={isExternal ? "noopener noreferrer" : undefined}
              className="text-primary hover:text-primary/80 underline decoration-primary/50 hover:decoration-primary transition-all duration-200 font-medium"
            >
              {children}
              {isExternal && (
                <ExternalLink className="inline-block w-3 h-3 ml-1 mb-0.5" />
              )}
            </a>
          );
        },
        blockquote: ({ children }) => (
          <blockquote className="rounded-lg bg-muted p-6 text-center italic font-bold mb-6 relative flex items-center">
            <div className="absolute left-0 top-0 h-full w-2 bg-primary opacity-100" />
            <div className="text-foreground/80 pl-8 flex-1 flex items-center justify-center">
              {children}
            </div>
          </blockquote>
        ),
        strong: ({ children }) => (
          <strong className="font-bold text-foreground">{children}</strong>
        ),
        em: ({ children }) => (
          <em className="italic text-foreground/90">{children}</em>
        ),
        code: ({
          children,
          inline = false,
          className,
          ...props
        }: React.ComponentProps<'code'> & { inline?: boolean }) => {
          const match = /language-(\w+)/.exec(className || "");
          const language = match ? match[1] : "text";

          return !inline ? (
            <div className="my-8 rounded-lg overflow-hidden border border-border shadow-sm">
              <div className="bg-muted px-4 py-2 text-sm font-medium text-foreground border-b border-border">
                {language}
              </div>
              <SyntaxHighlighter
                language={language}
                style={(theme === 'dark' ? oneDark : oneLight) as SyntaxHighlighterProps['style']}
                PreTag="div"
                customStyle={{
                  margin: 0,
                  padding: "1.5rem",
                  fontSize: "0.875rem",
                  lineHeight: "1.5",
                }}
              >
                {String(children).replace(/\n$/, "")}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code
              className="bg-muted px-2 py-1 rounded text-sm font-mono border border-border/50"
              {...props}
            >
              {children}
            </code>
          );
        },
        img: ({ src, alt }) => (
          <figure className="my-10">
            <div className="relative aspect-[16/9] w-full overflow-hidden rounded-lg border border-border shadow-md">
              <Image
                src={src || ""}
                alt={alt || ""}
                fill
                className="object-cover transition-transform duration-300 hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw"
              />
            </div>
            {alt && (
              <figcaption className="text-sm text-center mt-3 text-muted-foreground italic">
                {alt}
              </figcaption>
            )}
          </figure>
        ),
        hr: () => (
          <div className="my-4 flex items-center justify-center">
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
            <div className="mx-4 w-2 h-2 bg-primary rounded-full"></div>
            <div className="w-24 h-px bg-gradient-to-r from-transparent via-border to-transparent"></div>
          </div>
        ),
        table: ({ children }) => (
          <div className="my-8 w-full overflow-x-auto rounded-lg border border-border shadow-sm">
            <table className="min-w-full divide-y divide-border">
              {children}
            </table>
          </div>
        ),
        thead: ({ children }) => (
          <thead className="bg-muted/50">
            {children}
          </thead>
        ),
        tbody: ({ children }) => (
          <tbody className="bg-background divide-y divide-border">
            {children}
          </tbody>
        ),
        th: ({ children }) => (
          <th className="px-6 py-4 text-left text-sm font-semibold text-foreground">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="px-6 py-4 text-sm text-foreground/90">
            {children}
          </td>
        ),
      }}
    >
      {processedContent}
    </ReactMarkdown>
  );
}
