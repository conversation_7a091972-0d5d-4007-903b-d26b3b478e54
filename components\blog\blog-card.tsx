"use client"

import React from "react"
import { BlogPost } from "@/types/blog"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface CategoryData {
  name: string
  slug: string
  description?: string
  image: string
  color?: string
}

// Helper function to get category data by slug
function getCategoryBySlug(slug: string): CategoryData | null {
  const categoryMap: Record<string, CategoryData> = {
    'ecommerce': {
      name: 'Ecommerce',
      slug: 'ecommerce',
      description: 'Explore the latest trends, strategies, and insights in the world of e-commerce.',
      image: '/images/categories/ecommerce.jpg',
      color: '#4F46E5'
    },
    'small-business': {
      name: 'Small Business',
      slug: 'small-business',
      description: 'Practical advice and resources for small business owners.',
      image: '/images/categories/ecommerce.jpg',
      color: '#10B981'
    },
    'tech-sovereignty': {
      name: 'Tech Sovereignty',
      slug: 'tech-sovereignty',
      description: 'Digital independence and national security through technology.',
      image: '/images/categories/ecommerce.jpg',
      color: '#8B5CF6'
    },
    'swadesic': {
      name: 'Swadesic',
      slug: 'swadesic',
      description: 'Updates, features, and news about the Swadesic platform.',
      image: '/images/categories/ecommerce.jpg',
      color: '#DC2626'
    },
    'online-store': {
      name: 'Online Store',
      slug: 'online-store',
      description: 'Tips and strategies for building and managing successful online stores.',
      image: '/images/categories/ecommerce.jpg',
      color: '#059669'
    },
    'updates': {
      name: 'Updates',
      slug: 'updates',
      description: 'Latest updates, feature releases, and improvements.',
      image: '/images/categories/ecommerce.jpg',
      color: '#F59E0B'
    },
    'marketing': {
      name: 'Marketing',
      slug: 'marketing',
      description: 'Digital marketing strategies, social media tips, and growth hacks.',
      image: '/images/categories/ecommerce.jpg',
      color: '#3B82F6'
    },
    'entrepreneurship': {
      name: 'Entrepreneurship',
      slug: 'entrepreneurship',
      description: 'Inspiration and advice for entrepreneurs at every stage.',
      image: '/images/categories/ecommerce.jpg',
      color: '#EC4899'
    },
    'success-stories': {
      name: 'Success Stories',
      slug: 'success-stories',
      description: 'Real stories from businesses that have achieved remarkable success.',
      image: '/images/categories/ecommerce.jpg',
      color: '#F59E0B'
    },
    'business': {
      name: 'Business',
      slug: 'business',
      description: 'General business advice, strategies, and insights.',
      image: '/images/categories/ecommerce.jpg',
      color: '#7C3AED'
    }
  }

  return categoryMap[slug] || null
}

interface BlogCardProps {
  post: BlogPost
  featured?: boolean
  compact?: boolean
}

export function BlogCard({ post, featured = false, compact = false }: BlogCardProps) {
  // Get the first category with its data
  const categoryData = (() => {
    const firstCategory = post.categories?.[0] || 'Uncategorized'
    const categorySlug = firstCategory.toLowerCase().replace(/\s+/g, '-')
    const categoryInfo = getCategoryBySlug(categorySlug)

    return categoryInfo || {
      name: firstCategory,
      slug: categorySlug,
      color: '#6B7280',
      image: '/images/categories/ecommerce.jpg'
    }
  })()

  // Compact variant for sidebar
  if (compact) {
    return (
      <div className="group flex gap-3 p-3 rounded-lg border border-border hover:shadow-md transition-all duration-200 hover:bg-muted/50">
        <div className="relative w-20 h-16 flex-shrink-0 overflow-hidden rounded-md">
          <Link href={`/blog/${post.slug}`}>
            <Image
              src={post.coverImage}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes="80px"
            />
          </Link>
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span
              className="inline-block w-2 h-2 rounded-full flex-shrink-0"
              style={{ backgroundColor: categoryData.color }}
            />
            <span className="text-xs text-muted-foreground truncate">
              {categoryData.name}
            </span>
          </div>
          <h3 className="font-medium text-sm leading-tight line-clamp-2 mb-1">
            <Link
              href={`/blog/${post.slug}`}
              className="hover:text-primary transition-colors"
            >
              {post.title}
            </Link>
          </h3>
          <p className="text-xs text-muted-foreground">
            {new Date(post.date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
            })}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn(
      "group overflow-hidden bg-card rounded-lg border border-border hover:shadow-lg transition-all duration-300 hover:-translate-y-1",
      featured && "lg:flex lg:gap-6"
    )}>
      <div className={cn(
        "relative overflow-hidden",
        featured ? "lg:w-1/2 aspect-[16/10]" : "aspect-[16/10]",
        "rounded-t-lg lg:rounded-l-lg lg:rounded-t-none"
      )}>
        <Link href={`/blog/${post.slug}`} className="block w-full h-full">
          {post.coverImage ? (
            <Image
              src={post.coverImage.startsWith('http') ? post.coverImage : post.coverImage.startsWith('/') ? post.coverImage : `/${post.coverImage}`}
              alt={post.title}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              sizes={featured ? "(max-width: 768px) 100vw, 50vw" : "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"}
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <span className="text-gray-400 text-sm">No image</span>
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
        </Link>

        {/* Category badge */}
        <div className="absolute top-3 left-3 z-10">
          <Link
            href={`/blog/category/${categoryData.slug}`}
            className={cn(
              "inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium",
              "transition-all duration-200 hover:scale-105",
              "text-white backdrop-blur-sm shadow-sm"
            )}
            style={{
              backgroundColor: `${categoryData.color}E6`, // Add transparency
            }}
          >
            {categoryData.name}
          </Link>
        </div>
      </div>

      <div className={cn(
        "p-4",
        featured && "lg:w-1/2 lg:flex lg:flex-col lg:justify-center"
      )}>
        {/* Additional categories as small pills */}
        {post.categories.length > 1 && (
          <div className="flex flex-wrap gap-1.5 mb-3">
            {post.categories.slice(1, 3).map((category) => {
              const catSlug = category.toLowerCase().replace(/\s+/g, '-')
              const catData = getCategoryBySlug(catSlug) || {
                name: category,
                slug: catSlug,
                color: '#6B7280'
              }

              return (
                <Link
                  key={catSlug}
                  href={`/blog/category/${catSlug}`}
                  className={cn(
                    "text-xs px-2 py-0.5 rounded-full transition-all duration-200",
                    "hover:scale-105 hover:shadow-sm"
                  )}
                  style={{
                    backgroundColor: `${catData.color}15`,
                    color: catData.color,
                    border: `1px solid ${catData.color}30`
                  }}
                >
                  {category}
                </Link>
              )
            })}
            {post.categories.length > 3 && (
              <span className="text-xs text-muted-foreground">
                +{post.categories.length - 3} more
              </span>
            )}
          </div>
        )}

        <h3 className={cn(
          "font-bold leading-tight mb-2",
          featured ? "text-xl lg:text-2xl" : "text-lg"
        )}>
          <Link
            href={`/blog/${post.slug}`}
            className="hover:text-primary transition-colors line-clamp-2"
          >
            {post.title}
          </Link>
        </h3>

        {featured && post.excerpt && (
          <p className="text-muted-foreground mb-3 line-clamp-2">
            {post.excerpt}
          </p>
        )}

        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span className="font-medium">{post.author.name}</span>
          <span className="opacity-50">•</span>
          <time dateTime={post.date}>
            {new Date(post.date).toLocaleDateString("en-US", {
              month: "short",
              day: "numeric",
              year: "numeric",
            })}
          </time>
          <span className="opacity-50">•</span>
          <span>{Math.ceil(Number(post.readingTime) || 5)} min read</span>
        </div>
      </div>
    </div>
  )
}
