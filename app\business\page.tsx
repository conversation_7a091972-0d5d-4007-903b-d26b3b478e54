
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Container } from "@/components/ui/container"
import FeaturesSectionAdaptive from "@/components/features-section-adaptive"
import FeaturesSectionResponsiveImage from "@/components/features-section-responsive-image"
import FAQSection from "@/components/faq-section"
import { parseFAQs } from "@/lib/parse-faqs"
import fs from 'fs'
import path from 'path'
import { ArrowRight, ArrowUpRight } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import ImageFeatureCard from "@/components/image-feature-card"
import { HeroSlider, SlideData } from "@/components/hero-slider"

const sellerFeatures = {
  title: "Powerful Tools for Your Business",
  description: "Everything you need to manage and grow your business effectively",
  features: [
    {
      title: "Products and Content at One Place!",
      description: "Maximize your conversions keeping your products and content close to each other!",
      image: "/Swadesic-web-prep/seller/Store with Products and Content tab.jpg"
    },
    {
      title: "Products That Start Conversations and Drive Sales",
      description: "Display your products professionally with in-built comments section & past sales stats-showcasing trust with reviews and social proof!",
      image: "/Swadesic-web-prep/seller/product and its comments section with reviews & questions.jpg"
    },
    {
      title: "Complete Store Management",
      description: "Manage your store effortlessly with access to all controls and growing list of tools for your operations.",
      image: "/Swadesic-web-prep/seller/Store management & all controls.jpg"
    },
    {
      title: "Unified Experience for all your accounts",
      description: "Never miss an update! Get notified of new orders and all updates in one place.",
      image: "/Swadesic-web-prep/seller/Unified experience.jpg"
    },

    {
      title: "Order Management",
      description: "Organize your orders and track their status easily! Every update you make is notified to your customers and visible in their My orders.",
      image: "/Swadesic-web-prep/seller/Orders-tracking-and-management.jpg"
    },
    {
      title: "In-app Messaging",
      description: "In-app messaging helps you connect directly with your customers, ensuring privacy and ease of communication.",
      image: "/Swadesic-web-prep/app/In-app messaging.jpg"
    }

  ]
}

const discoveryFeatures = {
  title: "Show up to Right Customers in the Platform",
  description: "100X your visibility to customers onboarded by thousands of Swadesic sellers",
  features: [
    {
      title: "Appear in Store Recommendations",
      description: "Get your store recommended to customers in Store recommendations and state & category wise lists.",
      image: "/Swadesic-web-prep/app/discovery/store recommendations in platform.jpg"
    },
    {
      title: "Show case your Products in Recommendations",
      description: "Your products get featured in personalized recommendations to reach more customers.",
      image: "/Swadesic-web-prep/app/discovery/Recommended products.jpg"
    },
    {
      title: "Products accessible across Swadesic via Search",
      description: "Your products appear in relevant search results based on keywords in your product descriptions, making it easy for customers to find you.",
      image: "/Swadesic-web-prep/app/discovery/products-in-search-results.jpg"
    },
    {
      title: "Show up in Swadesic Feed with every product & post updates!",
      description: "Not just your content but also your products show up in Swadesic Feed to relevant people to get you more customers. Get mentioned in feeds and discussions, increasing your store's visibility.",
      image: "/Swadesic-web-prep/app/discovery/store & posts mentions in feed by others.jpg"
    },
    {
      title: "Be always available in Visit History",
      description: "Customers can quickly access to your store from their visit history.",
      image: "/Swadesic-web-prep/app/discovery/discovering-stores-in-visit-history.jpg"
    }
  ]
}

// Hero section slide data
const heroSlides: SlideData[] = [
  {
    headline: "Still Taking Orders on DMs? Build a Store That Works While You Sleep.",
    subheadline: "No more missed orders, manual tracking or payment drama. Swadesic gives your brand a professional storefront buyers trust — so you sell more, even being offline.",
    cta: "Create Your Store — at Free of Cost",
    label: "Instagram & WhatsApp Seller"
  },
  {
    headline: "The Best Store Internet Can Give You!",
    subheadline: "Run your business with a Store that has Community, Your Brand Identity, Order Management, Tracking for Buyers, Integrated Payments & Shipping, Notifications and more",
    cta: "Start Your Business Journey Today",
    label: "Business Seller"
  },  
  {
    headline: "Make Buyers Remember Your Name — not just the platform you sell in..",
    subheadline: "Empower your Brand Identity with Swadesic Store. Establish customers relationships via community directly on your Store. Why give away control? Own your online presence for long term success.",
    cta: "Claim your Branded Store — In 2 Minutes.",
    label: "Marketplace Seller"
  },
{
    headline: "Your Shop Is Local. Your Brand Can Be National.",
    subheadline: "Break the geography barrier. Swadesic gives your store a digital identity with built-in community, support, and nationwide reach.",
    cta: "Go Online and Go PAN India — at Free of Cost",
    label: "Physical Store Owner"
  }
]

export default async function BusinessPage() {
  // Read and parse seller FAQs
  const sellerFAQsPath = path.join(process.cwd(), 'public/Markdowns/seller/seller-faqs.md')
  const sellerFAQsContent = fs.readFileSync(sellerFAQsPath, 'utf8')
  const sellerFAQs = parseFAQs(sellerFAQsContent)

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        {/* Hero Section with Slider */}
        <div className="relative flex min-h-[70vh] items-start sm:items-center justify-center bg-white px-2 sm:px-6 lg:px-8 py-12">
          <HeroSlider slides={heroSlides} interval={7000} />
        </div>


        
        {/* Product Shots Section */}
        <div className="bg-white py-24">
          <Container>
            <div className="flex flex-col-reverse md:flex-row items-center gap-12">
              <div className="w-full md:w-1/2 text-center md:text-left">
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  Your Business HQ
                </h2>
                <p className="mt-4 text-base leading-8 text-muted-foreground">
                  Run your Swadesic store as your <b>Business Headquarters</b> bringing products, branding, and customers together. Promote your store to sell products and build a customer community at one place with full data control!
                </p>
              </div>
              <div className="w-full md:w-1/2">

                  <Image
                    src="/product-shots/store-products-feed.png"
                    alt="Store Products Feed"
                    width={1920}
                    height={1080}
                    className="w-full h-auto"
                    priority
                  />

              </div>
            </div>
          </Container>
        </div>

        {/* Pricing link below the slider
        <div className="absolute bottom-8 left-0 right-0 flex justify-center z-20">
              <Link
                href="/business/pricing"
                className="inline-flex items-center text-sm text-muted-foreground hover:text-foreground mx-auto justify-center"
              >
                View Pricing
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
          </div> */}
                        
        {/* Special Offers Link Section */}
        <div className="bg-gray-50 py-12">
          <Container>
            <div className="grid gap-8 md:grid-cols-2">
              <Link href="/zero-fee-selling" className="group">
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <h3 className="text-xl font-semibold mb-2 group-hover:text-primary">Become Most happening Local Business</h3>
                  <p className="text-muted-foreground">Be Discovered in your locality with Swadesic. No handling fee.</p>
                  <div className="mt-4 flex items-center text-primary">
                    Learn More <ArrowRight className="ml-2 h-4 w-4" />
                  </div>
                </div>
              </Link>

              {/* <Link href="/affiliate-program" className="group">
                <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <h3 className="text-xl font-semibold mb-2 group-hover:text-primary">Refer & Earn 20% Revenue Share</h3>
                  <p className="text-muted-foreground">Refer stores to Swadesic and earn 20% of our revenue share for life. Start earning passive income today.</p>
                  <div className="mt-4 flex items-center text-primary">
                    Learn More <ArrowRight className="ml-2 h-4 w-4" />
                  </div>
                </div>
              </Link> */}
            </div>
          </Container>
        </div>

        {/* Seller Features Section */}
        <FeaturesSectionAdaptive
          title={sellerFeatures.title}
          description={sellerFeatures.description}
          features={sellerFeatures.features}
          className="bg-gray-50"
        />

        {/* Discovery Features Section */}
        <div className="bg-gray-50 py-24">
          <Container>
            <div className="relative">
              {/* Header with overlapping style */}
              <div className="mx-auto max-w-2xl text-center mb-16">
                <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                  Show up to Right Customers Everywhere in the Platform
                </h2>
                <p className="text-base text-muted-foreground">
                  100X your visibility to customers onboarded by thousands of Swadesic sellers
                </p>
              </div>

              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
                {discoveryFeatures.features.map((feature) => (
                  <ImageFeatureCard
                    key={feature.title}
                    title={feature.title}
                    description={feature.description}
                    image={feature.image}
                  />
                ))}
              </div>

              {/* CTA Button */}
              <div className="mt-16 text-center">
                <Button
                  size="lg"
                  className="bg-green-400 text-white hover:bg-black/90 px-8 py-6 text-lg"
                >
                  Start Selling Now
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
          </Container>
        </div>

        {/* Order Management Section */}
        <FeaturesSectionResponsiveImage
          feature={{
            title: "Comprehensive Order Management",
            description: "Track and manage all your orders efficiently covering every scenario and realtime updates to you & your customers.",
            mobileImage: "/Swadesic-web-prep/order/every-orders-statuses-covered-mobile.jpg",
            desktopImage: "/Swadesic-web-prep/order/every-orders-statuses-covered-desktop.jpg"
          }}
          className="bg-gray-50"
        />

        {/* How It Works */}
        <Container className="py-8 md:py-12 lg:py-20 px-4 sm:px-6">
          <h2 className="text-2xl sm:text-3xl font-bold text-center mb-8 md:mb-12">How It Works</h2>
          <div className="grid gap-8 md:grid-cols-3">
            <Card className="p-6 flex flex-col items-center text-center">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <span className="text-xl font-bold text-primary">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Sign Up & Complete Onboarding</h3>
              <p className="text-muted-foreground">Create your account, set up your store, and complete onboarding. Once done, your store can go live!</p>
            </Card>
            <Card className="p-6 flex flex-col items-center text-center">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <span className="text-xl font-bold text-primary">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Go Live & Build Your Community</h3>
              <p className="text-muted-foreground">Post products, share content, and engage with customers. They can message you but orders require verification approval.</p>
            </Card>
            <Card className="p-6 flex flex-col items-center text-center">
              <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <span className="text-xl font-bold text-primary">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Get Verified & Start Selling</h3>
              <p className="text-muted-foreground">Once verified, start receiving orders and grow your Swadeshi business seamlessly.</p>
            </Card>
          </div>
        </Container>

        {/* FAQ Section */}
        <FAQSection
          title="Seller FAQs"
          description="Learn about selling your products on Swadesic"
          faqs={sellerFAQs}
        />

        {/* CTA Section */}
        <Container className="py-8 md:py-12 lg:py-20 px-4 sm:px-6">
          <Card className="p-6 sm:p-12 bg-primary text-primary-foreground text-center">
            <h2 className="text-2xl sm:text-3xl font-bold mb-4">
              Ready to Join Swadesic?
            </h2>
            <p className="mb-6 text-primary-foreground/90 max-w-[600px] mx-auto">
              Start growing your business and build a long lasting customer community today.
            </p>
            <Button size="lg" variant="secondary">
              Download the App
              <ArrowUpRight className="ml-2 h-4 w-4" />
            </Button>
          </Card>
        </Container>
      </main>
    </div>
  )
}
