import Link from "next/link"
import { NavigationMenu, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuContent, NavigationMenuTrigger, navigationMenuTriggerStyle } from "@/components/ui/navigation-menu"
import { cn } from "@/lib/utils"
import { mainNav } from "@/config/navigation"
import { motion } from "framer-motion"

export function MainNav() {
  return (
    <NavigationMenu className="max-w-full">
      <NavigationMenuList className="gap-2">
        {mainNav.map((item) => (
          <NavigationMenuItem key={item.title}>
            {item.href ? (
              <Link href={item.href} legacyBehavior passHref>
                <NavigationMenuLink className={cn(
                  navigationMenuTriggerStyle(),
                  "text-sm font-medium text-muted-foreground hover:text-foreground"
                )}>
                  {item.title}
                </NavigationMenuLink>
              </Link>
            ) : (
              <>
                <NavigationMenuTrigger className="text-sm font-medium text-muted-foreground hover:text-foreground">
                  {item.title}
                </NavigationMenuTrigger>
                <NavigationMenuContent className="overflow-hidden">
                  <motion.div 
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
                    className="fixed left-0 right-0 top-full w-screen bg-white shadow-lg border-b z-50"
                  >
                    <div className="mx-auto w-full max-w-7xl">
                      <motion.div 
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.1, duration: 0.2 }}
                        className="grid grid-cols-3 gap-x-8 gap-y-10 p-8"
                      >
                        {item.categories?.map((category, catIndex) => (
                          <motion.div 
                            key={category.title}
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: 0.1 + (catIndex * 0.05), duration: 0.2 }}
                          >
                            <h3 className="text-sm font-semibold text-muted-foreground mb-4">
                              {category.title}
                            </h3>
                            <div className="grid gap-4">
                              {category.items.map((subItem, itemIndex) => (
                                <motion.div
                                  key={subItem.title}
                                  initial={{ opacity: 0, x: -5 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ 
                                    delay: 0.15 + (catIndex * 0.03) + (itemIndex * 0.03),
                                    duration: 0.2 
                                  }}
                                >
                                  <Link
                                    href={subItem.href || "#"}
                                    {...(subItem.external ? { target: "_blank", rel: "noopener noreferrer" } : {})}
                                    className="group block"
                                  >
                                    <div className="text-base font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                                      {subItem.title}
                                    </div>
                                    {subItem.description && (
                                      <p className="mt-1 text-sm text-muted-foreground group-hover:text-foreground/80 transition-colors duration-200">
                                        {subItem.description}
                                      </p>
                                    )}
                                  </Link>
                                </motion.div>
                              ))}
                            </div>
                          </motion.div>
                        ))}
                      </motion.div>
                    </div>
                  </motion.div>
                </NavigationMenuContent>
              </>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  )
}
