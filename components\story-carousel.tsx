"use client"
import { useRef, useState, useEffect } from "react"
import dynamic from 'next/dynamic'
import { ArrowLeft, ArrowRight } from "lucide-react"
import { LucideProps } from 'lucide-react'

interface StoryPhase {
  iconName: string
  number: number
  title: string
  description: string
}

interface StoryCarouselProps {
  phases: StoryPhase[]
}

// Update the type to handle dynamic imports
const IconMap: { [key: string]: React.ComponentType<LucideProps> } = {
  Factory: dynamic(() => import('lucide-react').then(mod => mod.Factory)),
  LineChart: dynamic(() => import('lucide-react').then(mod => mod.LineChart)),
  IndianRupee: dynamic(() => import('lucide-react').then(mod => mod.IndianRupee))
}

export function StoryCarousel({ phases }: StoryCarouselProps) {
  const [currentPhase, setCurrentPhase] = useState(1)
  const scrollRef = useRef<HTMLDivElement>(null)

  // Function to handle dot navigation
  const scrollToPhase = (phaseNumber: number) => {
    if (!scrollRef.current) return
    
    const cards = scrollRef.current.children
    if (cards.length >= phaseNumber) {
      const targetCard = cards[phaseNumber - 1] as HTMLElement
      scrollRef.current.scrollTo({
        left: targetCard.offsetLeft - 16, // 16px for the container padding
        behavior: 'smooth'
      })
      setCurrentPhase(phaseNumber)
    }
  }

  // Update current phase based on scroll position
  useEffect(() => {
    const scrollContainer = scrollRef.current
    if (!scrollContainer) return

    const handleScroll = () => {
      const scrollLeft = scrollContainer.scrollLeft
      const containerWidth = scrollContainer.clientWidth

      // Find the card that's most visible in the viewport
      const cardIndex = Math.round(scrollLeft / containerWidth)
      const newPhase = Math.min(Math.max(1, cardIndex + 1), phases.length)
      
      if (currentPhase !== newPhase) {
        setCurrentPhase(newPhase)
      }
    }

    scrollContainer.addEventListener('scroll', handleScroll)
    return () => scrollContainer.removeEventListener('scroll', handleScroll)
  }, [currentPhase, phases.length])

  return (
    <div className="relative px-4">
      <div 
        ref={scrollRef}
        className="overflow-x-auto snap-x snap-mandatory scrollbar-none scroll-smooth flex gap-6 p-6"
      >
        {phases.map((phase, index) => {
          const Icon = IconMap[phase.iconName]
          return (
            <div key={index} className="snap-start shrink-0 w-[85vw] md:w-[350px] flex-none">
              <div className="bg-white rounded-2xl shadow-lg p-8 aspect-square relative border border-gray-200">
                <div className="absolute top-4 left-4">
                  <div className="bg-[var(--brand-color)]/10 rounded-lg px-3 py-1">
                    <span className="text-[var(--brand-color)] font-medium">Phase {phase.number}</span>
                  </div>
                </div>
                <div className="flex flex-col h-full justify-between pt-8">
                  {/* Header */}
                  <div>
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center shrink-0">
                        {Icon && <Icon className="w-6 h-6 text-primary" />}
                      </div>
                      <h3 className="text-2xl font-semibold">{phase.title}</h3>
                    </div>

                    {/* Content */}
                    <p className="text-muted-foreground text-base leading-relaxed">
                      {phase.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Scroll Indicators (Mobile Only) */}
      <div className="flex flex-col items-center gap-3 mt-6 mb-4 md:hidden px-4">
        {/* Navigation Buttons */}
        <div className="flex items-center gap-4">
          <button 
            onClick={() => scrollToPhase(Math.max(1, currentPhase - 1))}
            className="text-muted-foreground hover:text-primary transition-colors pr-4"
            aria-label="Previous phase"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          {/* Dots */}
          <div className="flex items-center gap-3">
            {phases.map((_, index) => (
              <button
                key={index}
                onClick={() => scrollToPhase(index + 1)}
                className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                  index + 1 === currentPhase
                    ? "bg-gray-400"
                    : "bg-gray-200 hover:bg-gray-400"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
          <button 
            onClick={() => scrollToPhase(Math.min(phases.length, currentPhase + 1))}
            className="text-muted-foreground hover:text-primary transition-colors pl-4"
            aria-label="Next phase"
          >
            <ArrowRight className="w-5 h-5" />
          </button>
        </div>
        
        {/* Dots
        <div className="flex items-center gap-3">
          {phases.map((_, index) => (
            <button
              key={index}
              onClick={() => scrollToPhase(index + 1)}
              className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                index + 1 === currentPhase
                  ? "bg-gray-400"
                  : "bg-gray-200 hover:bg-gray-400"
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div> */}
      </div>
    </div>
  )
}
