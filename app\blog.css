.prose-container {
  max-width: 100%;
  font-size: 1.125rem;
  line-height: 1.75;
}

.prose-container > * {
  max-width: 100%;
  margin-bottom: 1.5rem;
}

/* Headings */
.prose-container h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
}

.prose-container h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 2.5rem;
  margin-bottom: 1.25rem;
}

.prose-container h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose-container h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

/* Links */
.prose-container a {
  color: hsl(var(--primary));
  text-decoration: none;
}

.prose-container a:hover {
  text-decoration: underline;
}

/* Lists */
.prose-container ul,
.prose-container ol {
  padding-left: 1.5rem;
}

.prose-container li {
  margin-bottom: 0.5rem;
}

/* Blockquotes */
.prose-container blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding-left: 1rem;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* Code blocks */
.prose-container pre {
  background-color: hsl(var(--secondary));
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.prose-container code {
  font-family: monospace;
  background-color: hsl(var(--secondary));
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Images */
.prose-container img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
}

/* Allow certain elements to be full width */
.prose-container > pre,
.prose-container > figure,
.prose-container > div,
.prose-container > table {
  max-width: 100%;
}

/* Add scroll margin for headings */
.prose-container h1,
.prose-container h2,
.prose-container h3,
.prose-container h4 {
  scroll-margin-top: 100px; /* Adjust this value based on your header height */
}

/* Dark mode specific improvements */
.dark .prose-container img {
  opacity: 0.9;
}

.dark .prose-container pre {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.dark .prose-container code {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-secondary {
  scrollbar-color: hsl(var(--secondary)) transparent;
}

.scrollbar-track-transparent {
  scrollbar-color: hsl(var(--secondary)) transparent;
}

/* Webkit scrollbar styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: hsl(var(--secondary));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}

/* Table of Contents specific styles */
.toc-container {
  max-height: calc(100vh - 12rem);
  overflow-y: auto;
}

/* Remove padding - handled by Container component */
@media (max-width: 1024px) {
  .prose-container {
    padding: 0;
  }
}
