{"name": "swadesic-new", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint", "migrate-blog": "node scripts/migrate-blog-posts.js", "analyze": "cross-env ANALYZE=true next build", "analyze:server": "cross-env BUNDLE_ANALYZER=server next build", "analyze:browser": "cross-env BUNDLE_ANALYZER=browser next build"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.1.8", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.11.17", "gray-matter": "^4.0.3", "lucide-react": "^0.460.0", "next": "14.2.16", "next-themes": "^0.4.3", "react": "^18", "react-dom": "^18", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "reading-time": "^1.5.0", "rehype-autolink-headings": "^7.1.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.16", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.2.16"}}